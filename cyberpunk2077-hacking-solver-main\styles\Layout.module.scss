.container {
  min-height: 100vh;
  padding: 0 0.5rem;
  display: flex;
  flex-direction: column;
  //justify-content: center;
  align-items: center;
}

.backdrop {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;

  &::before {
    position: absolute;
    content: "";
    left: -50vh;
    right: -50vh;
    bottom: -70%;
    height: 80%;
    background: radial-gradient(
      ellipse closest-side,
      rgba(208, 237, 87, 0.45) 0%,
      rgba(0, 0, 0, 0) 100%
    );
  }
}

.footer {
  width: 100%;
  height: 100px;
  border-top: 1px solid rgba(200, 200, 100, 0.2);

  &__content {
    display: flex;
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}

.footer img {
  margin-left: 0.5rem;
}

.footer {
  color: rgba(150, 150, 130, 0.5);
}

.copyright {
  text-align: right;
  margin-bottom: 0;
  margin-left: auto;
}

.privacy-link,
.github-link {
  margin-right: 2rem;
}
