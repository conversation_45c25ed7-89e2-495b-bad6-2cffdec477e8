{"name": "cyberpunk-solver-web", "version": "0.1.0", "private": true, "license": "Apache-2.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "analyze": "ANALYZE=true next build", "test": "jest", "test:watch": "jest --watch", "lint": "tslint --project ."}, "dependencies": {"bootstrap": "^5.3.3", "classnames": "^2.3.2", "lodash": "^4.17.21", "next": "^12.3.1", "react": "^17.0.2", "react-bootstrap": "^2.10.4", "react-dom": "^17.0.2", "react-sortable-hoc": "^2.0.0", "tesseract.js": "^3.0.3"}, "devDependencies": {"@next/bundle-analyzer": "^12.3.1", "@testing-library/jest-dom": "^5.11.8", "@testing-library/react": "^11.2.2", "@types/jest": "^29.2.0", "@types/lodash": "^4.14.186", "@types/node": "^16.11.68", "@types/node-fetch": "^2.6.2", "@types/react": "^17.0.50", "@types/react-bootstrap": "^0.32.25", "jest": "^29.2.1", "sass": "^1.55.0", "ts-jest": "^29.0.3", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "typescript": "^4.5.5", "worker-loader": "^3.0.8"}}