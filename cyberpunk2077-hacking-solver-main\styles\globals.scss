@import "~bootstrap/scss/bootstrap";
@import "~bootstrap/scss/functions";
@import "~bootstrap/scss/variables";
@import "~bootstrap/scss/mixins/_breakpoints";
@import "./common";

@font-face {
  font-family: "Rajdhani";
  src: url("/fonts/subset-Rajdhani-SemiBold.woff2") format("woff2"),
    url("/fonts/subset-Rajdhani-SemiBold.woff") format("woff"),
    url("/fonts/subset-Rajdhani-SemiBold.ttf") format("truetype"),
    url("/fonts/subset-Rajdhani-SemiBold.svg#Rajdhani-SemiBold") format("svg");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Rajdhani";
  src: url("/fonts/subset-Rajdhani-Medium.woff2") format("woff2"),
    url("/fonts/subset-Rajdhani-Medium.woff") format("woff"),
    url("/fonts/subset-Rajdhani-Medium.ttf") format("truetype"),
    url("/fonts/subset-Rajdhani-Medium.svg#Rajdhani-Medium") format("svg");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Rajdhani";
  src: url("/fonts/subset-Rajdhani-Regular.woff2") format("woff2"),
    url("/fonts/subset-Rajdhani-Regular.woff") format("woff"),
    url("/fonts/subset-Rajdhani-Regular.ttf") format("truetype"),
    url("/fonts/subset-Rajdhani-Regular.svg#Rajdhani-Regular") format("svg");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Rajdhani Mod";
  src: url("/fonts/subset-Rajdhani-Mod-Medium.woff2") format("woff2"),
    url("/fonts/subset-Rajdhani-Mod-Medium.woff") format("woff"),
    url("/fonts/Rajdhani Mod Medium.ttf") format("truetype"),
    url("/fonts/subset-Rajdhani-Mod-Medium.svg#Rajdhani-Mod-Medium")
      format("svg");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Rajdhani Mod";
  src: url("/fonts/subset-Rajdhani-Mod-SemiBold.woff2") format("woff2"),
    url("/fonts/subset-Rajdhani-Mod-SemiBold.woff") format("woff"),
    url("/fonts/Rajdhani Mod SemiBold.ttf") format("truetype"),
    url("/fonts/subset-Rajdhani-Mod-SemiBold.svg#Rajdhani-Mod-SemiBold")
      format("svg");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

$color-highlight: #d0ed57;

html,
body {
  padding: 0;
  margin: 0;
  color: #fff;
  background-color: rgb(25, 17, 25);
  background: linear-gradient(
    176deg,
    rgba(25, 17, 25, 1) 0%,
    rgba(13, 15, 17, 1) 100%
  );

  // calibri
  @include body-font;
  font-weight: 400;

  font-size: 13px;
  @include media-breakpoint-up(md) {
    font-size: 14px;
  }
  @include media-breakpoint-up(lg) {
    font-size: 16px;
  }

  //position: relative;
  /*font-family: "Roboto", -apple-system, BlinkMacSystemFont, Segoe UI, Roboto,
    Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;*/
}

#__next {
  position: relative;
}

a {
  color: inherit;
  text-decoration: none;
}

a:hover {
  color: #5ee9f2;
}

* {
  box-sizing: border-box;
}

.modal {
  &-content {
    background-color: $color-bg;
    color: $color-highlight;
    border: 2px solid $color-highlight;
    border-radius: 0;
    border-bottom-left-radius: 2px;
    border-bottom-right-radius: 2px;
  }

  &-header {
    border-radius: 0;
    background: $color-highlight;
    color: $color-bg;
    border-bottom: none;
  }

  &-footer {
    border-top: 1px solid $color-highlight;
  }
}
