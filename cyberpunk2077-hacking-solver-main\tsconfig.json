{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext", "webworker"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "downlevelIteration": true, "incremental": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "pages/_app.js"], "exclude": ["node_modules"]}