@use "sass:color";
@import "./common";

.title {
  margin-left: -3px;
  margin-bottom: 3rem;
}

.description {
  line-height: 1.5;
  font-size: 1.5rem;
  color: $color-highlight;
  text-transform: uppercase;
  font-weight: 500;
  margin-bottom: 1rem;
}

.description-separator {
  height: 0.5rem;
  background: $color-highlight;
  width: 100%;
  margin-bottom: 4rem;
  box-shadow: 20px 14px 28px rgba(208, 237, 87, 0.25),
    -20px 14px 28px rgba(208, 237, 87, 0.25),
    0 10px 10px rgba(208, 237, 87, 0.22), 0 0 40px 5px rgb(22, 90, 13);
}

.main {
  padding: 9vh 1rem;
}

.hackbox {
  border: 2px solid $color-highlight;
  background: transparent;
  margin-bottom: 2rem;

  &__header {
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    padding: 0 0 0 2rem;
    height: 42px;
    background: $color-highlight;
    color: $color-bg;
    position: relative;
  }

  &__sequence {
    position: absolute;
    display: none;
    left: 0;
    font-weight: 600;
    font-size: 2rem;
    line-height: 2rem;
    width: 4rem;
    height: 4rem;
    transform: translateX(-50%);
    box-sizing: border-box;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 0;
    overflow: hidden;
    color: $color-highlight;
    border: 2px solid $color-highlight;
    background: #151017;
    box-shadow: 0 0 30px rgba(22, 90, 13, 0.3), 0 0 6px rgba(22, 90, 13, 0.5);
    text-shadow: 0 0 10px rgba(208, 237, 87, 0.5);
  }

  &__sequence::before {
    content: "";
    position: absolute;
    left: 28px;
    right: 0;
    top: 12px;
    bottom: 0;
    filter: blur(10px);

    box-shadow: inset 0 0 30px rgba(22, 90, 13, 0.5),
      inset 0 0 6px rgba(22, 90, 13, 0.7),
      inset 0 -20px 30px rgba(208, 237, 87, 0.7);
  }

  @include media-breakpoint-up(xs) {
    &__sequence {
      display: flex;
    }
    &__header {
      padding-left: 5rem;
    }
  }

  &__header_text {
    margin: 0;
    padding: 0;
    font-size: 1.5rem;
  }

  &__inside {
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;
    justify-content: center;
  }
}

.sequence-box {
  border: 2px solid $color-highlight;
  background: transparent;
  min-height: 12rem;
  margin-bottom: 2rem;

  &__header {
    font-weight: 400;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    padding: 0 0 0 2rem;
    height: 42px;
    background: $color-highlight;
    color: $color-bg;
    position: relative;
  }

  &__header-text {
    margin: 0;
    padding: 0;
    font-size: 1.5rem;
  }

  &__sequence {
    display: none;
    position: absolute;
    left: 0;
    font-weight: 600;
    font-size: 2rem;
    line-height: 2rem;
    width: 4rem;
    height: 4rem;
    transform: translateX(-50%);
    box-sizing: border-box;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 0;
    overflow: hidden;
    color: $color-highlight;
    border: 2px solid $color-highlight;
    background: #151017;
    box-shadow: 0 0 30px rgba(22, 90, 13, 0.3), 0 0 6px rgba(22, 90, 13, 0.5);
    text-shadow: 0 0 10px rgba(208, 237, 87, 0.5);
  }

  @include media-breakpoint-up(xs) {
    &__sequence {
      display: flex;
    }
  }

  &__sequence::before {
    content: "";
    position: absolute;
    left: -30px;
    right: 10px;
    top: 12px;
    bottom: 0;
    filter: blur(10px);

    box-shadow: inset 0 0 30px rgba(22, 90, 13, 0.2),
      inset 0 0 6px rgba(22, 90, 13, 0.3),
      inset 0 -20px 30px rgba(208, 237, 87, 0.3);
  }

  @include media-breakpoint-between(sm, md) {
    &__header {
      padding-left: 5rem;
    }
  }

  @include media-breakpoint-up(lg) {
    &__sequence {
      right: 0;
      left: initial;
      transform: translateX(50%);
    }
  }

  &__inside {
    //padding: 1rem;
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;
    justify-content: center;
  }
}

.separator {
  border-top: 1px solid #333;
}

.report-issue {
  font-size: 1.1em;

  a:not(:hover) {
    color: color.adjust(#5ee9f2, $alpha: -0.3);
  }
}

.ocr-output-container {
  display: none;
}
