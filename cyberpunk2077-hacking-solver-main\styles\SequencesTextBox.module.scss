@use "sass:color";
@import "./common";

.sequences-selector {
  @include code-font;
  flex: 1 1 auto;
  height: 25rem;

  border-radius: 3px;
  border: 1px solid color.adjust($color-lighter-bg, $alpha: -0.3);
  background: transparent;
  color: $color-highlight;

  padding: 1rem 10%;

  @include media-breakpoint-up(lg) {
    padding: 1rem 2rem;
  }

  text-transform: uppercase;

  font-size: 1.4rem;
  line-height: 1.7em;
  word-spacing: 0.4rem;

  @include media-breakpoint-up(sm) {
    font-size: 1.5rem;
    line-height: 1.8em;
    word-spacing: 0.7rem;
  }

  @include media-breakpoint-up(lg) {
    font-size: 1.6rem;
    line-height: 1.9em;
    word-spacing: 0.8rem;
  }

  &__error {
    padding: 0.5rem 1rem;
    font-weight: 500;
    color: $color-error;
    border-top: 2px solid $color-error;
  }
}
